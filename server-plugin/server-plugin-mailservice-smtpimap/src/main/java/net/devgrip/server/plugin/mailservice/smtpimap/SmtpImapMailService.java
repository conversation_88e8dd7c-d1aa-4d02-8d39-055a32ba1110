package net.devgrip.server.plugin.mailservice.smtpimap;

import net.devgrip.server.AppServer;
import net.devgrip.server.annotation.Editable;
import net.devgrip.server.annotation.Password;
import net.devgrip.server.model.support.administration.mailservice.MailService;
import net.devgrip.server.model.support.administration.mailservice.SmtpExplicitSsl;
import net.devgrip.server.model.support.administration.mailservice.SmtpSslSetting;
import net.devgrip.server.mail.*;
import org.jetbrains.annotations.Nullable;

import javax.mail.Message;
import javax.validation.constraints.Email;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Collection;
import java.util.concurrent.Future;
import java.util.function.Consumer;

@Editable(name="SMTP/IMAP", order=100)
public class SmtpImapMailService implements MailService {

	private static final long serialVersionUID = 1L;
	
	private String smtpHost;

	private SmtpSslSetting sslSetting = new SmtpExplicitSsl();

	private String smtpUser;

	private String smtpPassword;
	
	private String systemAddress;

	private InboxPollSetting inboxPollSetting;

	private int timeout = 60;
	
	private transient MailPosition mailPosition;

	@Editable(order=100, name="SmtpImapMailService.smtpHost.name", description = "SmtpImapMailService.smtpHost.desc")
	@NotEmpty
	public String getSmtpHost() {
		return smtpHost;
	}

	public void setSmtpHost(String smtpHost) {
		this.smtpHost = smtpHost;
	}

	@Editable(order=200, name="SmtpImapMailService.ssl.name", description = "SmtpImapMailService.ssl.desc")
	@NotNull
	public SmtpSslSetting getSslSetting() {
		return sslSetting;
	}

	public void setSslSetting(SmtpSslSetting sslSetting) {
		this.sslSetting = sslSetting;
	}

	@Editable(order=300, name="SmtpImapMailService.smtpUser.name")
	public String getSmtpUser() {
		return smtpUser;
	}

	public void setSmtpUser(String smtpUser) {
		this.smtpUser = smtpUser;
	}

	@Editable(order=400, name="SmtpImapMailService.smtpPwd.name")
	@Password(autoComplete="new-password")
	public String getSmtpPassword() {
		return smtpPassword;
	}

	public void setSmtpPassword(String smtpPassword) {
		this.smtpPassword = smtpPassword;
	}

	@Editable(order=425, name= "MailService.systemUserAddress.name", description= "MailService.systemUserAddress.desc")
	@Email
	@NotEmpty
	@Override
	public String getSystemAddress() {
		return systemAddress;
	}

	public void setSystemAddress(String systemAddress) {
		this.systemAddress = systemAddress;
	}

	@Editable(order=450, name= "MailService.checkIncoming.name", description= "MailService.checkIncoming.desc")
	public InboxPollSetting getInboxPollSetting() {
		return inboxPollSetting;
	}

	public void setInboxPollSetting(InboxPollSetting inboxPollSetting) {
		this.inboxPollSetting = inboxPollSetting;
	}

	@Editable(order=10000, name= "MailService.timeout.name",description= "MailService.timeout.desc")
	@Min(value=5, message= "{MailService.timeout.message}")
	public int getTimeout() {
		return timeout;
	}

	public void setTimeout(int timeout) {
		this.timeout = timeout;
	}

	private SmtpSetting getSmtpSetting() {
		MailCredential smtpCredential;
		if (smtpPassword != null)
			smtpCredential = new BasicAuthPassword(smtpPassword);
		else
			smtpCredential = null;
		return new SmtpSetting(smtpHost, sslSetting, smtpUser, smtpCredential, getTimeout());
	}
	
	@Override
	public void sendMail(Collection<String> toList, Collection<String> ccList, Collection<String> bccList, 
						 String subject, String htmlBody, String textBody, @Nullable String replyAddress, 
						 @Nullable String senderName, @Nullable String references, boolean testMode) {
		getMailManager().sendMail(getSmtpSetting(), toList, ccList, bccList, subject, htmlBody, textBody, 
				replyAddress, senderName, getSystemAddress(), references);
	}

	@Override
	public InboxMonitor getInboxMonitor(boolean testMode) {
		if (inboxPollSetting != null) {
			var imapUser = inboxPollSetting.getImapUser();
			var imapCredential = new BasicAuthPassword(inboxPollSetting.getImapPassword());
			var imapSetting = new ImapSetting(inboxPollSetting.getImapHost(), inboxPollSetting.getSslSetting(),
					imapUser, imapCredential, inboxPollSetting.getPollInterval(), getTimeout());
			return new InboxMonitor() {
				@Override
				public Future<?> monitor(Consumer<Message> messageConsumer, boolean testMode) {
					if (mailPosition == null)
						mailPosition = new MailPosition();
					return getMailManager().monitorInbox(imapSetting, getSystemAddress(),
							messageConsumer, mailPosition, testMode);
				}
			};
		} else {
			return null;
		}
	}

	private MailManager getMailManager() {
		return AppServer.getInstance(MailManager.class);
	}
	
}
