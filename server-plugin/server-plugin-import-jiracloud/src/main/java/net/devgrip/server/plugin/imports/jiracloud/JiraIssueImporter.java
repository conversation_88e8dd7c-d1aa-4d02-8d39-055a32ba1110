package net.devgrip.server.plugin.imports.jiracloud;

import com.google.common.collect.Lists;
import net.devgrip.commons.utils.TaskLogger;
import net.devgrip.server.imports.IssueImporter;
import net.devgrip.server.web.component.taskbutton.TaskResult;
import net.devgrip.server.web.util.ImportStep;
import org.apache.wicket.model.ResourceModel;

import java.io.Serializable;
import java.util.List;

public class JiraIssueImporter implements IssueImporter {

	private static final long serialVersionUID = 1L;

	private final ImportStep<ImportServer> serverStep = new ImportStep<ImportServer>() {

		private static final long serialVersionUID = 1L;

		@Override
		public String getTitle() {
			return new ResourceModel("JiracloudProjectImporter.title.step1").getObject();
		}

		@Override
		protected ImportServer newSetting() {
			return new ImportServer();
		}
		
	};
	
	private final ImportStep<ImportProject> projectStep = new ImportStep<ImportProject>() {

		private static final long serialVersionUID = 1L;

		@Override
		public String getTitle() {
			return new ResourceModel("IssueImporter.title.chooseProject").getObject();
		}

		@Override
		protected ImportProject newSetting() {
			ImportProject project = new ImportProject();
			project.server = serverStep.getSetting();
			return project;
		}
		
	};
	
	private final ImportStep<ImportOption> optionStep = new ImportStep<ImportOption>() {

		private static final long serialVersionUID = 1L;

		@Override
		public String getTitle() {
			return new ResourceModel("IssueImporter.title.step3").getObject();
		}

		@Override
		protected ImportOption newSetting() {
			return serverStep.getSetting().buildImportOption(
					Lists.newArrayList(projectStep.getSetting().getProject()));		
		}
		
	};
	
	@Override
	public String getName() {
		return JiraModule.NAME;
	}

	@Override
	public TaskResult doImport(Long projectId, boolean dryRun, TaskLogger logger) {
		ImportServer server = serverStep.getSetting();
		String jiraProject = projectStep.getSetting().getProject();
		ImportOption option = optionStep.getSetting();
		return server.importIssues(projectId, jiraProject, option, dryRun, logger);
	}

	@Override
	public List<ImportStep<? extends Serializable>> getSteps() {
		return Lists.newArrayList(serverStep, projectStep, optionStep);
	}

}
