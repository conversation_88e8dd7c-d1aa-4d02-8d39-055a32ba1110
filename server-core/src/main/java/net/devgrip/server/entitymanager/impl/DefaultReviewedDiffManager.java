package net.devgrip.server.entitymanager.impl;

import net.devgrip.server.entitymanager.ReviewedDiffManager;
import net.devgrip.server.event.Listen;
import net.devgrip.server.event.system.SystemStarting;
import net.devgrip.server.event.system.SystemStopping;
import net.devgrip.server.model.ReviewedDiff;
import net.devgrip.server.model.User;
import net.devgrip.server.persistence.TransactionManager;
import net.devgrip.server.persistence.annotation.Sessional;
import net.devgrip.server.persistence.annotation.Transactional;
import net.devgrip.server.persistence.dao.BaseEntityManager;
import net.devgrip.server.persistence.dao.Dao;
import net.devgrip.server.taskschedule.SchedulableTask;
import net.devgrip.server.taskschedule.TaskScheduler;
import net.devgrip.server.util.concurrent.BatchWorkManager;
import net.devgrip.server.util.concurrent.BatchWorker;
import net.devgrip.server.util.concurrent.Prioritized;
import org.hibernate.criterion.Restrictions;
import org.joda.time.DateTime;
import org.quartz.CronScheduleBuilder;
import org.quartz.ScheduleBuilder;

import javax.inject.Inject;
import javax.inject.Singleton;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static net.devgrip.server.model.ReviewedDiff.PROP_NEW_COMMIT_HASH;
import static net.devgrip.server.model.ReviewedDiff.PROP_OLD_COMMIT_HASH;
import static net.devgrip.server.model.ReviewedDiff.PROP_USER;
import static net.devgrip.server.util.Constants.DEFAULT_CLEANUP_PRIORITY;

@Singleton
public class DefaultReviewedDiffManager extends BaseEntityManager<ReviewedDiff> 
		implements ReviewedDiffManager, SchedulableTask {
	
	private static final int MAX_PRESERVE_DAYS = 365;

	private final BatchWorkManager batchWorkManager;

	private final TransactionManager transactionManager;
	
	private final TaskScheduler taskScheduler;
	
	private volatile String taskId;
	
	@Inject
	public DefaultReviewedDiffManager(Dao dao, TaskScheduler taskScheduler,
									  BatchWorkManager batchWorkManager,
									  TransactionManager transactionManager) {
		super(dao);
		this.taskScheduler = taskScheduler;
		this.batchWorkManager = batchWorkManager;
		this.transactionManager = transactionManager;
	}

	@Sessional
	@Override
	public Map<String, ReviewedDiff> query(User user, String oldCommitHash, String newCommitHash) {
		var statuses = new HashMap<String, ReviewedDiff>();
		var criteria = newCriteria();
		criteria.add(Restrictions.eq(PROP_USER, user));
		criteria.add(Restrictions.eq(PROP_OLD_COMMIT_HASH, oldCommitHash));
		criteria.add(Restrictions.eq(PROP_NEW_COMMIT_HASH, newCommitHash));
		for (var status: query(criteria)) 
			statuses.put(status.getBlobPath(), status);
		return statuses;
	}

	@Transactional
	@Override
	public void createOrUpdate(ReviewedDiff status) {
		dao.persist(status);
	}

	@Listen
	public void on(SystemStarting event) {
		taskId = taskScheduler.schedule(this);
	}

	@Listen
	public void on(SystemStopping event) {
		if (taskId != null) {
			taskScheduler.unschedule(taskId);
		}
	}

	@Override
	public void execute() {
		batchWorkManager.submit(new BatchWorker("reviewed-diff-manager-cleanup") {

			@Override
			public void doWorks(List<Prioritized> works) {
				transactionManager.run(() -> {
					var query = getSession().createQuery("delete from ReviewedDiff where date < :date");
					query.setParameter("date", new DateTime().minusDays(MAX_PRESERVE_DAYS).toDate());
					query.executeUpdate();
				});
			}

		}, new Prioritized(DEFAULT_CLEANUP_PRIORITY));
	}

	@Override
	public ScheduleBuilder<?> getScheduleBuilder() {
		return CronScheduleBuilder.dailyAtHourAndMinute(0, 0);
	}
	
}
