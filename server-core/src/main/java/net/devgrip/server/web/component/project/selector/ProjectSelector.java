package net.devgrip.server.web.component.project.selector;

import com.google.common.base.Preconditions;
import net.devgrip.server.AppServer;
import net.devgrip.server.entitymanager.ProjectManager;
import net.devgrip.server.model.Project;
import net.devgrip.server.util.Similarities;
import net.devgrip.server.util.facade.ProjectCache;
import net.devgrip.server.web.WebConstants;
import net.devgrip.server.web.asset.selectbytyping.SelectByTypingResourceReference;
import net.devgrip.server.web.behavior.InputChangeBehavior;
import net.devgrip.server.web.behavior.infinitescroll.InfiniteScrollBehavior;
import net.devgrip.server.web.component.link.PreventDefaultAjaxLink;
import net.devgrip.server.web.component.project.ProjectAvatar;
import net.devgrip.server.web.component.tabbable.AjaxActionTab;
import net.devgrip.server.web.component.tabbable.Tab;
import net.devgrip.server.web.component.tabbable.Tabbable;
import net.devgrip.server.web.page.project.blob.ProjectBlobPage;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.markup.html.AjaxLink;
import org.apache.wicket.behavior.AttributeAppender;
import org.apache.wicket.markup.ComponentTag;
import org.apache.wicket.markup.head.CssHeaderItem;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.JavaScriptHeaderItem;
import org.apache.wicket.markup.head.OnDomReadyHeaderItem;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.TextField;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.markup.repeater.RepeatingView;
import org.apache.wicket.model.*;
import org.apache.wicket.request.cycle.RequestCycle;
import org.apache.wicket.request.http.WebRequest;
import org.apache.wicket.request.http.WebResponse;
import org.apache.wicket.request.mapper.parameter.PageParameters;

import javax.annotation.Nullable;
import javax.servlet.http.Cookie;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

public abstract class ProjectSelector extends Panel {

	private final IModel<List<Project>> projectsModel;
	private final IModel<List<Project>> favoritesProjectsModel;

	private static final String COOKIE_PROJECT_SELECTOR_TAB = "project.selector.tab";

	private final IModel<List<Project>> similarProjectsModel = new LoadableDetachableModel<List<Project>>() {

		@Override
		protected List<Project> load() {
			ProjectCache cache = getProjectManager().cloneCache();
			return new Similarities<>(projectsModel.getObject()) {

				@Override
				protected double getSimilarScore(Project item) {
					return cache.getSimilarScore(item, searchInput);
				}

			};
		}

	};
	private final IModel<List<Project>> similarFavProjectsModel = new LoadableDetachableModel<List<Project>>() {

		@Override
		protected List<Project> load() {
			List<Project> favProjectList = favoritesProjectsModel.getObject();
			if (favProjectList.isEmpty()) {
				return new ArrayList<>();
			}else{
				ProjectCache cache = getProjectManager().cloneCache();
				return new Similarities<>(favProjectList) {

					@Override
					protected double getSimilarScore(Project item) {
						return cache.getSimilarScore(item, searchInput);
					}

				};
			}
		}

	};

	private RepeatingView projectsView;
	private TextField<String> searchField;	
	
	private String searchInput;
	
	public ProjectSelector(String id, IModel<List<Project>> projectsModel) {
		super(id);
		
		this.projectsModel = projectsModel;
		this.favoritesProjectsModel = new LoadableDetachableModel<List<Project>>() {
			@Override
			protected List<Project> load() {
				return List.of();
			}
		};
	}
	public ProjectSelector(String id, IModel<List<Project>> projectsModel, IModel<List<Project>> favoriteProjectsModel) {
		super(id);

		this.projectsModel = projectsModel;
		this.favoritesProjectsModel = favoriteProjectsModel;
	}

	@Override
	protected void onInitialize() {
		super.onInitialize();
		
		add(new Label("title", new AbstractReadOnlyModel<>() {
			@Override
			public Object getObject() {
				return getTitle();
			}
		}) {
			@Override
			protected void onConfigure() {
				super.onConfigure();
				setVisible(getTitle() != null);
			}
		});

		searchField = new TextField<>("search", Model.of(""));
		searchField.setOutputMarkupId(true);
		add(searchField);

		searchField.add(new InputChangeBehavior() {

			@Override
			protected void onInputChange(AjaxRequestTarget target) {
				searchInput = searchField.getInput();
				newView(target);
			}

		});

		String selectedTab = getSelectedTabFromCookie();
		List<Tab> tabs = new ArrayList<>();
		AjaxActionTab projectsTab = new AjaxActionTab(new ResourceModel("All")) {

			@Override
			protected void onSelect(AjaxRequestTarget target, Component tabLink) {
				onSelectTab(target, "all");
			}

		};
		tabs.add(projectsTab);
		AjaxActionTab favProjectsTab = new AjaxActionTab(new ResourceModel("ProjectSelector.defaultQuery")) {

			@Override
			protected void onSelect(AjaxRequestTarget target, Component tabLink) {
				onSelectTab(target, "defaultQuery");
			}

		};
		tabs.add(favProjectsTab);
		if ("all".equals(selectedTab)) {
			projectsTab.setSelected(true);
		} else {
			favProjectsTab.setSelected(true);
		}

		add(new Tabbable("tabs", tabs));

		newView(null);

		setOutputMarkupId(true);
	}

	/**
	 * Read cookie to determine which tab to show initially
	 * cookie value:
	 *  - all
	 *  - defaultQuery
	 * @return the cookie value
	 */
	private String getSelectedTabFromCookie() {
		WebRequest request = (WebRequest) RequestCycle.get().getRequest();
		Cookie cookie = request.getCookie(COOKIE_PROJECT_SELECTOR_TAB);
		String selectedTab = cookie != null ? cookie.getValue() : "all";
		return selectedTab;
	}

	/**
	 * save current cookie
	 * @param value the cookie value
	 *         - all
	 *         - defaultQuery
	 *
	 */
	private void saveCookie(String value) {
		Preconditions.checkArgument(List.of("all", "defaultQuery").contains(value), "Invalid cookie value: " + value);
		WebResponse response = (WebResponse) RequestCycle.get().getResponse();
		Cookie cookie = new Cookie(COOKIE_PROJECT_SELECTOR_TAB, value);
		cookie.setPath("/");
		cookie.setMaxAge(Integer.MAX_VALUE);
		response.addCookie(cookie);
	}

	private boolean isFavTabActive() {
		return Objects.equals(getSelectedTabFromCookie(), "defaultQuery");
	}

	private void onSelectTab(AjaxRequestTarget target, String cookie) {
		saveCookie(cookie);

		searchField.setModel(Model.of(""));
		searchInput = null;
		target.add(searchField);
		newView(target);
	}

	private void newView(@Nullable AjaxRequestTarget target) {
		List<Project> projectsModel = isFavTabActive() ? similarFavProjectsModel.getObject() : similarProjectsModel.getObject();
		Map<Long, String> projectIdAndPath = projectsModel.stream().collect(Collectors.toMap(Project::getId, Project::getPath));
		WebMarkupContainer projectsContainer = new WebMarkupContainer("projects") {

			@Override
			protected void onBeforeRender() {
				projectsView = new RepeatingView("projects");
				int index = 0;
				for (Project lazyProject: projectsModel) {
					Component item = newItem(projectsView.newChildId(), lazyProject, projectIdAndPath);
					projectsView.add(item);
					if (++index >= WebConstants.PAGE_SIZE)
						break;
				}
				addOrReplace(projectsView);

				super.onBeforeRender();
			}

			@Override
			protected void onConfigure() {
				super.onConfigure();
				setVisible(!projectsModel.isEmpty());
			}

		};
		projectsContainer.add(new InfiniteScrollBehavior(WebConstants.PAGE_SIZE) {
			Map<Long, String> projectIdAndPath = projectsModel.stream().collect(Collectors.toMap(Project::getId, Project::getPath));
			@Override
			protected void appendMore(AjaxRequestTarget target, int offset, int count) {
				for (int i=offset; i<offset+count; i++) {
					if (i >= projectsModel.size())
						break;
					Project lazyProject = projectsModel.get(i);
					Component item = newItem(projectsView.newChildId(), lazyProject, projectIdAndPath);
					projectsView.add(item);
					String script = String.format("$('#%s').append('<li id=\"%s\"></li>');",
							projectsContainer.getMarkupId(), item.getMarkupId());
					target.prependJavaScript(script);
					target.add(item);
				}
			}

		});
		projectsContainer.setOutputMarkupPlaceholderTag(true);

		WebMarkupContainer noProjectsContainer = new WebMarkupContainer("noProjects") {

			@Override
			protected void onConfigure() {
				super.onConfigure();
				setVisible(projectsModel.isEmpty());
			}

		};
		noProjectsContainer.setOutputMarkupPlaceholderTag(true);

		if (target != null) {
			replace(projectsContainer);
			replace(noProjectsContainer);
			target.add(projectsContainer);
			target.add(noProjectsContainer);
		} else {
			add(projectsContainer);
			add(noProjectsContainer);
		}

	}
	
	private Component newItem(String componentId, Project lazyProject, Map<Long, String> projectIdAndPath) {
		WebMarkupContainer item = new WebMarkupContainer(componentId);

		Long projectId = lazyProject.getId();

		AjaxLink<Void> link = new PreventDefaultAjaxLink<Void>("link") {

			@Override
			public void onClick(AjaxRequestTarget target) {
				onSelect(target, getProjectManager().load(projectId));
			}
			
			@Override
			protected void onComponentTag(ComponentTag tag) {
				super.onComponentTag(tag);
				
				PageParameters params = ProjectBlobPage.paramsOf(getProjectManager().load(projectId));
				tag.put("href", urlFor(ProjectBlobPage.class, params).toString());
			}
			
		};
		if (lazyProject.equals(getCurrent()))
			link.add(AttributeAppender.append("class", " current"));
		link.add(new ProjectAvatar("avatar", projectId));
		link.add(new Label("path", projectIdAndPath.get(projectId)));
		item.add(link);
		
		return item;
	}
	
	private ProjectManager getProjectManager() {
		return AppServer.getInstance(ProjectManager.class);
	}

	@Override
	protected void onDetach() {
		similarProjectsModel.detach();
		similarFavProjectsModel.detach();
		projectsModel.detach();
		favoritesProjectsModel.detach();

		super.onDetach();
	}

	@Override
	public void renderHead(IHeaderResponse response) {
		super.renderHead(response);
		
		response.render(JavaScriptHeaderItem.forReference(new SelectByTypingResourceReference()));
		response.render(CssHeaderItem.forReference(new ProjectSelectorCssResourceReference()));
		
		String script = String.format("$('#%s').selectByTyping($('#%s'));", searchField.getMarkupId(), getMarkupId());
		response.render(OnDomReadyHeaderItem.forScript(script));
	}
	
	@Nullable
	protected Project getCurrent() {
		return null;
	}
	
	@Nullable
	protected String getTitle() {
		return null;
	}
	
	protected abstract void onSelect(AjaxRequestTarget target, Project project);
	
}
