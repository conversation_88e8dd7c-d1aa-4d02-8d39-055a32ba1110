package net.devgrip.server.web.component.user.passwordedit;

import net.devgrip.server.annotation.CurrentPassword;
import net.devgrip.server.annotation.Editable;
import net.devgrip.server.annotation.Password;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

@Editable
public class PasswordEditBean implements Serializable {

	private static final long serialVersionUID = 1L;

	private String oldPassword;
	
	private String newPassword;

	@Editable(name="passwordEditBean.oldPassword",order=100)
	@CurrentPassword
	@Password
	@NotEmpty
	public String getOldPassword() {
		return oldPassword;
	}

	public void setOldPassword(String oldPassword) {
		this.oldPassword = oldPassword;
	}

	@Editable(name="passwordEditBean.newPassword",order=200)
	@Password(useRule =true)
	@NotEmpty
	public String getNewPassword() {
		return newPassword;
	}

	public void setNewPassword(String newPassword) {
		this.newPassword = newPassword;
	}
	
}
