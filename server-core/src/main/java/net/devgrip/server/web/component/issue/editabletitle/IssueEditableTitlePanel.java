package net.devgrip.server.web.component.issue.editabletitle;

import net.devgrip.server.AppServer;
import net.devgrip.server.entitymanager.IssueChangeManager;
import net.devgrip.server.entityreference.LinkTransformer;
import net.devgrip.server.model.Issue;
import net.devgrip.server.model.Project;
import net.devgrip.server.security.SecurityUtils;
import net.devgrip.server.web.asset.emoji.Emojis;
import net.devgrip.server.web.behavior.ReferenceInputBehavior;
import net.devgrip.server.web.component.issue.progress.IssueProgressPanel;
import net.devgrip.server.web.component.link.copytoclipboard.CopyToClipboardLink;
import net.devgrip.server.web.page.base.BasePage;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.markup.html.AjaxLink;
import org.apache.wicket.ajax.markup.html.form.AjaxButton;
import org.apache.wicket.behavior.AttributeAppender;
import org.apache.wicket.markup.head.CssHeaderItem;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.TextField;
import org.apache.wicket.markup.html.panel.Fragment;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.LoadableDetachableModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.ResourceModel;

import static net.devgrip.server.entityreference.ReferenceUtils.transformReferences;

public abstract class IssueEditableTitlePanel extends Panel {

	private static final String CONTENT_ID = "content";
	
	public IssueEditableTitlePanel(String id) {
		super(id);
	}

	private Fragment newTitleEditor() {
		Fragment titleEditor = new Fragment(CONTENT_ID, "titleEditFrag", this);
		
		Form<?> form = new Form<Void>("form");
		form.add(AttributeAppender.append("class", new LoadableDetachableModel<String>() {

			@Override
			protected String load() {
				if (form.hasError())
					return "is-invalid";	
				else
					return "";
			}
			
		}));
		
		TextField<String> titleInput = new TextField<String>("title", Model.of(getIssue().getTitle()));
		titleInput.add(new ReferenceInputBehavior() {

			@Override
			protected Project getProject() {
				return getIssue().getProject();
			}
			
		});
		titleInput.setRequired(true);
		titleInput.setLabel(new ResourceModel("IssueEditableTitlePanel.title"));
		
		form.add(titleInput);
		
		form.add(new AjaxButton("save") {

			@Override
			protected void onSubmit(AjaxRequestTarget target, Form<?> form) {
				super.onSubmit(target, form);
				
				AppServer.getInstance(IssueChangeManager.class).changeTitle(getIssue(), titleInput.getModelObject());
				((BasePage)getPage()).notifyObservablesChange(target, getIssue().getChangeObservables(false));
				
				Fragment titleViewer = newTitleViewer();
				titleEditor.replaceWith(titleViewer);
				target.add(titleViewer);
			}

			@Override
			protected void onError(AjaxRequestTarget target, Form<?> form) {
				super.onError(target, form);
				target.add(titleEditor);
			}
			
		});
		
		form.add(new AjaxLink<Void>("cancel") {

			@Override
			public void onClick(AjaxRequestTarget target) {
				Fragment titleViewer = newTitleViewer();
				titleEditor.replaceWith(titleViewer);
				target.add(titleViewer);
			}
			
		});		
		
		titleEditor.add(form);
		
		titleEditor.setOutputMarkupId(true);
		
		return titleEditor;
	}
	
	private Fragment newTitleViewer() {
		Fragment titleViewer = new Fragment(CONTENT_ID, "titleViewFrag", this);
		titleViewer.add(new Label("title", new LoadableDetachableModel<String>() {

			@Override
			protected String load() {
				var transformed = transformReferences(getIssue().getTitle(), getIssue().getProject(), 
						new LinkTransformer(null));
				return Emojis.getInstance().apply(transformed) + " (" + getIssue().getReference().toString(getProject()) + ")";
			}
			
		}).setEscapeModelStrings(false));
		
		titleViewer.add(new WebMarkupContainer("confidential") {

			@Override
			protected void onConfigure() {
				super.onConfigure();
				setVisible(getIssue().isConfidential());
			}
			
		});
		
		titleViewer.add(new AjaxLink<Void>("edit") {

			@Override
			public void onClick(AjaxRequestTarget target) {
				Fragment titleEditor = newTitleEditor();
				titleViewer.replaceWith(titleEditor);
				target.add(titleEditor);
			}

			@Override
			protected void onConfigure() {
				super.onConfigure();

				setVisible(SecurityUtils.canModifyIssue(getIssue()));
			}
			
		});
		titleViewer.add(new CopyToClipboardLink("copy", 
				Model.of(getIssue().getTitle() + " (" + getIssue().getReference().toString(getProject()) + ")")));
		
		titleViewer.add(new IssueProgressPanel("progress") {

			@Override
			protected Issue getIssue() {
				return IssueEditableTitlePanel.this.getIssue();
			}
			
		});

		titleViewer.setOutputMarkupId(true);
		
		return titleViewer;
	}
	
	@Override
	protected void onInitialize() {
		super.onInitialize();
		
		add(newTitleViewer());
	}

	@Override
	public void renderHead(IHeaderResponse response) {
		super.renderHead(response);
		response.render(CssHeaderItem.forReference(new IssueEditableTitleCssResourceReference()));
	}

	protected abstract Issue getIssue();
	
	protected abstract Project getProject();
	
}
