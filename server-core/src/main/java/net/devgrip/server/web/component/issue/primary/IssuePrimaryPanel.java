package net.devgrip.server.web.component.issue.primary;

import net.devgrip.server.AppServer;
import net.devgrip.server.attachment.AttachmentSupport;
import net.devgrip.server.attachment.ProjectAttachmentSupport;
import net.devgrip.server.entitymanager.IssueChangeManager;
import net.devgrip.server.entitymanager.IssueManager;
import net.devgrip.server.entitymanager.IssueReactionManager;
import net.devgrip.server.entitymanager.LinkSpecManager;
import net.devgrip.server.model.Issue;
import net.devgrip.server.model.LinkSpec;
import net.devgrip.server.model.Project;
import net.devgrip.server.model.User;
import net.devgrip.server.model.support.EntityReaction;
import net.devgrip.server.search.entity.issue.IssueQuery;
import net.devgrip.server.search.entity.issue.IssueQueryParseOption;
import net.devgrip.server.security.SecurityUtils;
import net.devgrip.server.util.DateUtils;
import net.devgrip.server.util.EmailAddressUtils;
import net.devgrip.server.util.LinkDescriptor;
import net.devgrip.server.util.LinkGroup;
import net.devgrip.server.util.criteria.Criteria;
import net.devgrip.server.web.ajaxlistener.ConfirmClickListener;
import net.devgrip.server.web.ajaxlistener.ConfirmLeaveListener;
import net.devgrip.server.web.behavior.ChangeObserver;
import net.devgrip.server.web.component.comment.CommentPanel;
import net.devgrip.server.web.component.comment.ReactionSupport;
import net.devgrip.server.web.component.floating.FloatingPanel;
import net.devgrip.server.web.component.issue.IssueStateBadge;
import net.devgrip.server.web.component.issue.choice.IssueChoiceProvider;
import net.devgrip.server.web.component.issue.create.NewIssueEditor;
import net.devgrip.server.web.component.issue.operation.TransitionMenuLink;
import net.devgrip.server.web.component.markdown.ContentVersionSupport;
import net.devgrip.server.web.component.menu.MenuItem;
import net.devgrip.server.web.component.menu.MenuLink;
import net.devgrip.server.web.component.modal.ModalPanel;
import net.devgrip.server.web.component.tabbable.AjaxActionTab;
import net.devgrip.server.web.component.tabbable.Tab;
import net.devgrip.server.web.component.tabbable.Tabbable;
import net.devgrip.server.web.component.user.ident.Mode;
import net.devgrip.server.web.component.user.ident.UserIdentPanel;
import net.devgrip.server.web.page.base.BasePage;
import net.devgrip.server.web.page.project.issues.detail.IssueActivitiesPage;
import net.devgrip.server.web.util.DeleteCallback;
import org.apache.wicket.Component;
import org.apache.wicket.Session;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.attributes.AjaxRequestAttributes;
import org.apache.wicket.ajax.markup.html.AjaxLink;
import org.apache.wicket.ajax.markup.html.form.AjaxButton;
import org.apache.wicket.behavior.AttributeAppender;
import org.apache.wicket.core.request.handler.IPartialPageRequestHandler;
import org.apache.wicket.markup.head.CssHeaderItem;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.markup.html.form.FormComponentPanel;
import org.apache.wicket.markup.html.link.BookmarkablePageLink;
import org.apache.wicket.markup.html.list.ListItem;
import org.apache.wicket.markup.html.list.ListView;
import org.apache.wicket.markup.html.panel.Fragment;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.markup.repeater.RepeatingView;
import org.apache.wicket.model.IModel;
import org.apache.wicket.model.LoadableDetachableModel;
import org.apache.wicket.model.ResourceModel;
import org.apache.wicket.model.StringResourceModel;
import org.apache.wicket.request.cycle.RequestCycle;
import org.apache.wicket.request.http.WebRequest;
import org.apache.wicket.request.http.WebResponse;
import org.unbescape.html.HtmlEscape;

import javax.annotation.Nullable;
import javax.servlet.http.Cookie;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static net.devgrip.server.security.SecurityUtils.canAccessIssue;
import static org.apache.wicket.model.Model.ofMap;

public abstract class IssuePrimaryPanel extends Panel {

	private static final String ISSUE_LINK_TAB_COOKIE = "issue.link.tab";

	private final IModel<List<LinkSpec>> linkSpecsModel = new LoadableDetachableModel<>() {
		@Override
		protected List<LinkSpec> load() {
			return getLinkSpecManager().queryAndSort();
		}

	};

	private final IModel<List<LinkDescriptor>> addibleLinkDescriptorsModel = new LoadableDetachableModel<>() {
		@Override
		protected List<LinkDescriptor> load() {
			var linkDescriptors = new ArrayList<LinkDescriptor>();
			for (LinkSpec spec: linkSpecsModel.getObject()) {
				if (SecurityUtils.canEditIssueLink(getProject(), spec)) {
					if (spec.getOpposite() != null) {
						if (spec.getOpposite().getParsedIssueQuery(getProject()).matches(getIssue()))
							linkDescriptors.add(new LinkDescriptor(spec, false));
						if (spec.getParsedIssueQuery(getProject()).matches(getIssue()))
							linkDescriptors.add(new LinkDescriptor(spec, true));	
					} else if (spec.getParsedIssueQuery(getProject()).matches(getIssue())) {
						linkDescriptors.add(new LinkDescriptor(spec, false));
					}
				}
			}	
			return linkDescriptors;		
		}
	};

	private final IModel<List<LinkGroup>> linkGroupsModel = new LoadableDetachableModel<>() {

		@Override
		protected List<LinkGroup> load() {
			List<LinkGroup> linkGroups = new ArrayList<>();
			for (LinkSpec spec : linkSpecsModel.getObject()) {
				if (spec.getOpposite() != null) {
					var targetIssues = getIssue().findLinkedIssues(spec, false).stream().filter(it->canAccessIssue(it)).collect(Collectors.toList());
					if (!targetIssues.isEmpty())
						linkGroups.add(new LinkGroup(new LinkDescriptor(spec, false), targetIssues));
					var sourceIssues = getIssue().findLinkedIssues(spec, true).stream().filter(it->canAccessIssue(it)).collect(Collectors.toList());
					if (!sourceIssues.isEmpty())
						linkGroups.add(new LinkGroup(new LinkDescriptor(spec, true), sourceIssues));
				} else {
					var issues = getIssue().findLinkedIssues(spec, false).stream().filter(it->canAccessIssue(it)).collect(Collectors.toList());
					if (!issues.isEmpty())
						linkGroups.add(new LinkGroup(new LinkDescriptor(spec, false), issues));
				}
			}
			return linkGroups;
		}

	};

	public IssuePrimaryPanel(String id) {
		super(id);
	}
	
	@Override
	protected void onInitialize() {
		super.onInitialize();
	
		Issue issue = getIssue();
		add(new UserIdentPanel("submitterAvatar", issue.getSubmitter(), Mode.AVATAR));
		add(new Label("submitterName", issue.getSubmitter().getDisplayName()));
		add(new Label("submitDate", DateUtils.formatAge(issue.getSubmitDate()))
			.add(new AttributeAppender("title", DateUtils.formatDateTime(issue.getSubmitDate()))));

		if (issue.getOnBehalfOf() != null) {
			Map<String,Object> myParams = new HashMap<>();
			myParams.put("behalf", HtmlEscape.escapeHtml5(EmailAddressUtils.describe(issue.getOnBehalfOf(), SecurityUtils.canManageIssues(getProject()))));
			StringResourceModel stringResourceModel = new StringResourceModel("IssuePrimaryPanel.onbehalfOf", ofMap(myParams));

			add(new Label("submitOnBehalfOf", stringResourceModel).setEscapeModelStrings(false));
		} else {
			add(new WebMarkupContainer("submitOnBehalfOf").setVisible(false));
		}

		add(new CommentPanel("description") {
			
			@Override
			protected String getComment() {
				return getIssue().getDescription();
			}

			@Override
			protected void onSaveComment(AjaxRequestTarget target, String comment) {
				AppServer.getInstance(IssueChangeManager.class).changeDescription(getIssue(), comment);
				((BasePage)getPage()).notifyObservablesChange(target, getIssue().getChangeObservables(false));
			}

			@Override
			protected List<User> getParticipants() {
				return getIssue().getParticipants();
			}
			
			@Override
			protected Project getProject() {
				return getIssue().getProject();
			}

			@Override
			protected AttachmentSupport getAttachmentSupport() {
				return new ProjectAttachmentSupport(getProject(), getIssue().getUUID(), 
						SecurityUtils.canManageIssues(getProject()));
			}

			@Override
			protected boolean canManageComment() {
				return SecurityUtils.canModifyIssue(getIssue());
			}

			@Override
			protected String getRequiredLabel() {
				return null;
			}

			@Override
			protected String getEmptyDescription() {
				return new ResourceModel("No_Description", "No description").getObject();
			}

			@Override
			protected ContentVersionSupport getContentVersionSupport() {
				return () -> 0;
			}

			@Override
			protected DeleteCallback getDeleteCallback() {
				return null;
			}

			@Override
			protected String getAutosaveKey() {
				return "issue:" + getIssue().getId() + ":description";
			}

			@Override
			protected ReactionSupport getReactionSupport() {
				return new ReactionSupport() {

					@Override
					public Collection<? extends EntityReaction> getReactions() {
						return getIssue().getReactions();
					}

					@Override
					public void onToggleEmoji(AjaxRequestTarget target, String emoji) {
						AppServer.getInstance(IssueReactionManager.class).toggleEmoji(
							SecurityUtils.getUser(),
							getIssue(),
							emoji);
					}

				};
			}

			@Override
			protected Component newMoreActions(String componentId) {
				var fragment = new Fragment(componentId, "linkIssuesActionFrag", IssuePrimaryPanel.this) {
					@Override
					protected void onConfigure() {
						super.onConfigure();
						setVisible(!addibleLinkDescriptorsModel.getObject().isEmpty());
					}
				};
				fragment.add(new MenuLink("linkIssues") {


					@Override
					protected List<MenuItem> getMenuItems(FloatingPanel dropdown) {
						List<MenuItem> menuItems = new ArrayList<>();
						for (LinkDescriptor descriptor: addibleLinkDescriptorsModel.getObject()) {
							var spec = descriptor.getSpec();
							var specId = spec.getId();
							var opposite = descriptor.isOpposite();
							var linkName = spec.getName(opposite);
							menuItems.add(new MenuItem() {
								@Override
								public String getLabel() {
									return linkName;
								}

								@Override
								public WebMarkupContainer newLink(String id) {
									return new AjaxLink<Void>(id) {
										@Override
										public void onClick(AjaxRequestTarget target) {
											dropdown.close();
											onLinkIssue(target, specId, opposite, linkName);
										}
									};
								};
							});
						}
						return menuItems;
					}
				});
				return fragment;
			}

		});
        
		var linksContainer = new WebMarkupContainer("links") {

			@Override
			protected void onConfigure() {
				super.onConfigure();
				setVisible(!linkGroupsModel.getObject().isEmpty());
			}

		};
		linksContainer.add(new ChangeObserver() {
			
			@Override
			protected Collection<String> findObservables() {
				return Collections.singleton(Issue.getDetailChangeObservable(getIssue().getId()));
			}

		});
		linksContainer.setOutputMarkupPlaceholderTag(true);
		add(linksContainer);
		linksContainer.add(new ListView<LinkGroup>("links", linkGroupsModel) {

			@Override
			protected void populateItem(ListItem<LinkGroup> item) {
				var group = item.getModelObject();
				var descriptor = group.getDescriptor();
				var spec = descriptor.getSpec();
				var specId = spec.getId();
				var opposite = descriptor.isOpposite();
				
				boolean canEditIssueLink = SecurityUtils.canEditIssueLink(getProject(), spec);
				
				String linkName = spec.getName(opposite);
				item.add(new Label("name", linkName));
				
				RepeatingView linkedIssuesView = new RepeatingView("linkedIssues");
				for (Issue linkedIssue: group.getIssues()) {
					LinkDeleteListener deleteListener;
					if (canEditIssueLink) { 
						deleteListener = new LinkDeleteListener() {
	
							@Override
							void onDelete(AjaxRequestTarget target, Issue linkedIssue) {
								getIssueChangeManager().removeLink(getLinkSpecManager().load(specId), getIssue(), 
										linkedIssue, opposite);
								notifyIssueChange(target, getIssue());
							}
							
						};
					} else {
						deleteListener = null;
					}
					linkedIssuesView.add(newLinkedIssueContainer(linkedIssuesView.newChildId(), 
							linkedIssue, deleteListener));
				}
				item.add(linkedIssuesView);

				boolean applicable;
				if (spec.getOpposite() != null) {
					if (opposite)
						applicable = spec.getParsedIssueQuery(getProject()).matches(getIssue());
					else
						applicable = spec.getOpposite().getParsedIssueQuery(getProject()).matches(getIssue());
				} else {
					applicable = spec.getParsedIssueQuery(getProject()).matches(getIssue());
				}

				item.add(new AjaxLink<Void>("addLink") {
					@Override
					public void onClick(AjaxRequestTarget target) {
						onLinkIssue(target, specId, opposite, linkName);
					}
				}.setVisible(applicable && canEditIssueLink));
			}
			
		});
	}

	private void onLinkIssue(AjaxRequestTarget target, Long specId, boolean opposite, String linkName) {
		var spec = getLinkSpecManager().load(specId);
		if (!opposite && !spec.isMultiple() && getIssue().findLinkedIssue(spec, false) != null
				|| opposite && !spec.getOpposite().isMultiple() && getIssue().findLinkedIssue(spec, true) != null) {
			Map<String,Object> myParams = new HashMap<>();
			myParams.put("specName", spec.getName(opposite));
			StringResourceModel stringResourceModel = new StringResourceModel("IssuePrimaryPanel.linkError.alreadyLinked", ofMap(myParams));
			Session.get().error(stringResourceModel.getString());
		} else {
			new ModalPanel(target) {

				private FormComponentPanel<Issue> newCreateNewPanel(String componentId) {
					var editor = new NewIssueEditor(componentId) {
						@Override
						protected Criteria<Issue> getTemplate() {
							String query;
							var spec = getLinkSpecManager().load(specId);
							if (opposite)
								query = spec.getOpposite().getIssueQuery();
							else
								query = spec.getIssueQuery();
							return IssueQuery.parse(getProject(), query, new IssueQueryParseOption(), false).getCriteria();
						}
						
						@Override
						protected Project getProject() {
							return getIssue().getProject();
						}
					};
					editor.setOutputMarkupId(true);														
					return editor;
				}

				private FormComponent<Issue> newLinkExistingPanel(String componentId) {
					return new SelectIssuePanel(componentId) {

						@Override
						protected IssueChoiceProvider getChoiceProvider() {
							return new IssueChoiceProvider() {
								@Override
								protected Project getProject() {
									return getIssue().getProject();
								}
								
								@Override
								protected IssueQuery getBaseQuery() {
									LinkSpec spec = getLinkSpecManager().load(specId);
									if (opposite) 
										return spec.getOpposite().getParsedIssueQuery(getProject());
									else 
										return spec.getParsedIssueQuery(getProject());
								}
								
							};						
						}
					};
				}

				private FormComponent<Issue> issuePopulator;

				@Override
				protected Component newContent(String id) {
					var frag = new Fragment(id, "addLinkFrag", IssuePrimaryPanel.this);
					
					var form = new Form<Void>("form");
					frag.add(form);
					// Read cookie to determine which tab to show initially
					WebRequest request = (WebRequest) RequestCycle.get().getRequest();
					Cookie cookie = request.getCookie(ISSUE_LINK_TAB_COOKIE);
					String selectedTab = cookie != null ? cookie.getValue() : "create";

					List<Tab> tabs = new ArrayList<>();

					AjaxActionTab createNew = new AjaxActionTab(new ResourceModel("IssuePrimaryPanel.createNew")) {
						@Override
						protected void onSelect(AjaxRequestTarget target, Component tabLink) {
							issuePopulator = newCreateNewPanel("tabContent");
							form.replace(issuePopulator);
							target.add(issuePopulator);

							WebResponse response = (WebResponse) RequestCycle.get().getResponse();
							Cookie cookie = new Cookie(ISSUE_LINK_TAB_COOKIE, "create");
							cookie.setPath("/");
							cookie.setMaxAge(Integer.MAX_VALUE);
							response.addCookie(cookie);
						}
					};
					tabs.add(createNew);

					AjaxActionTab selectExisting = new AjaxActionTab(new ResourceModel("IssuePrimaryPanel.selectExisting")) {
						@Override
						protected void onSelect(AjaxRequestTarget target, Component tabLink) {
							issuePopulator = newLinkExistingPanel("tabContent");
							form.replace(issuePopulator);
							target.add(issuePopulator);

							WebResponse response = (WebResponse) RequestCycle.get().getResponse();
							Cookie cookie = new Cookie(ISSUE_LINK_TAB_COOKIE, "select");
							cookie.setPath("/");
							cookie.setMaxAge(Integer.MAX_VALUE);
							response.addCookie(cookie);
						}
					};
					tabs.add(selectExisting);

					if ("select".equals(selectedTab)) {
						selectExisting.setSelected(true);
					} else {
						createNew.setSelected(true);
					}

					form.add(new Tabbable("tabs", tabs));

					if ("select".equals(selectedTab)) {
						issuePopulator = newLinkExistingPanel("tabContent");
					} else {
						issuePopulator = newCreateNewPanel("tabContent");
					}
					form.add(issuePopulator);
					Map<String,Object> myParams = new HashMap<>();
					myParams.put("linkName", linkName);
					StringResourceModel stringResourceModel = new StringResourceModel("IssuePrimaryPanel.addLinkTitle", ofMap(myParams));

					form.add(new Label("title", stringResourceModel));

					form.add(new AjaxButton("save") {
						@Override
						protected void onSubmit(AjaxRequestTarget target, Form<?> form) {
							var linkIssue = issuePopulator.getConvertedInput();
							if (linkIssue.isNew()) {
								getIssueManager().open(linkIssue);
								notifyIssueChange(target, linkIssue);
								var spec = getLinkSpecManager().load(specId);
								getIssueChangeManager().addLink(spec, getIssue(), linkIssue, opposite);
								notifyIssueChange(target, getIssue());
								close();							
							} else {
								LinkSpec spec = getLinkSpecManager().load(specId);
								if (getIssue().getId().equals(linkIssue.getId())) {
									form.error(new ResourceModel("IssuePrimaryPanel.linkError.canNotLinktoSelf", "Can not link to self").getObject());
									target.add(form);
								} else if (getIssue().findLinkedIssues(spec, opposite).contains(linkIssue)) {
									form.error(new ResourceModel("IssuePrimaryPanel.linkError.alreadyLinked2", "Issue already linked").getObject());
									target.add(form);
								} else {
									getIssueChangeManager().addLink(spec, getIssue(), linkIssue, opposite);
									notifyIssueChange(target, getIssue());
									close();
								}	
							}
						}

						@Override
						protected void onError(AjaxRequestTarget target, Form<?> form) {
							target.add(form);
						}
					});
					form.add(new AjaxLink<Void>("close") {
						@Override
						protected void updateAjaxAttributes(AjaxRequestAttributes attributes) {
							super.updateAjaxAttributes(attributes);
							attributes.getAjaxCallListeners().add(new ConfirmLeaveListener(form));
						}
						@Override
						public void onClick(AjaxRequestTarget target) {
							close();
						}
					});
					form.add(new AjaxLink<Void>("cancel") {
						@Override
						protected void updateAjaxAttributes(AjaxRequestAttributes attributes) {
							super.updateAjaxAttributes(attributes);
							attributes.getAjaxCallListeners().add(new ConfirmLeaveListener(form));
						}
						@Override
						public void onClick(AjaxRequestTarget target) {
							close();
						}
					});
					return frag;
				}
				
				@Override
				protected String getCssClass() {
					return "modal-lg";
				}
			};
		}
	}
	
	private Component newLinkedIssueContainer(String componentId, Issue linkedIssue, 
			@Nullable LinkDeleteListener deleteListener) {
		if (canAccessIssue(linkedIssue)) {
			Long linkedIssueId = linkedIssue.getId();
			Fragment fragment = new Fragment(componentId, "linkedIssueFrag", this);

			var link = new BookmarkablePageLink<Void>("title", IssueActivitiesPage.class, 
					IssueActivitiesPage.paramsOf(linkedIssue));
			link.add(new Label("label", linkedIssue.getTitle() + " (" + linkedIssue.getReference().toString(getProject()) + ")"));
			fragment.add(link);
			
			fragment.add(new AjaxLink<Void>("unlink") {

				@Override
				protected void updateAjaxAttributes(AjaxRequestAttributes attributes) {
					super.updateAjaxAttributes(attributes);
					attributes.getAjaxCallListeners().add(new ConfirmClickListener(
							new ResourceModel("IssuePrimaryPanel.removeLinkConfirm", "Do you really want to remove this link?").getObject()));
				}

				@Override
				public void onClick(AjaxRequestTarget target) {
					Issue linkedIssue = getIssueManager().load(linkedIssueId);
					deleteListener.onDelete(target, linkedIssue);
					notifyIssueChange(target, getIssue());
				}
				
			}.setVisible(deleteListener != null));

			AjaxLink<Void> stateLink = new TransitionMenuLink("state") {

				@Override
				protected Issue getIssue() {
					return getIssueManager().load(linkedIssueId);
				}

			};

			stateLink.add(new IssueStateBadge("badge", new LoadableDetachableModel<>() {
				@Override
				protected Issue load() {
					return getIssueManager().load(linkedIssueId);
				}
			}, true).add(AttributeAppender.append("class", "badge-sm")));
			
			fragment.add(stateLink);
			
			return fragment;
		} else {
			return new WebMarkupContainer(componentId).setVisible(false);
		}
	}

	private Project getProject() {
		return getIssue().getProject();
	}

	@Override
	public void renderHead(IHeaderResponse response) {
		super.renderHead(response);
		response.render(CssHeaderItem.forReference(new IssuePrimaryCssResourceReference()));
	}

	@Override
	protected void onDetach() {
		linkSpecsModel.detach();
		addibleLinkDescriptorsModel.detach();
		linkGroupsModel.detach();
		super.onDetach();
	}

	private LinkSpecManager getLinkSpecManager() {
		return AppServer.getInstance(LinkSpecManager.class);
	}

	private IssueManager getIssueManager() {
		return AppServer.getInstance(IssueManager.class);
	}
	
	private IssueChangeManager getIssueChangeManager() {
		return AppServer.getInstance(IssueChangeManager.class);
	}
	
	private void notifyIssueChange(IPartialPageRequestHandler handler, Issue issue) {
		((BasePage)getPage()).notifyObservablesChange(handler, issue.getChangeObservables(true));
	}

    protected abstract Issue getIssue();

	private static abstract class LinkDeleteListener implements Serializable {
		
		abstract void onDelete(AjaxRequestTarget target, Issue linkedIssue);
		
	}
	
}
