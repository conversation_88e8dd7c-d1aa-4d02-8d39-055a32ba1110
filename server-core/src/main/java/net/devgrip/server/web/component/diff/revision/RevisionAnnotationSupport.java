package net.devgrip.server.web.component.diff.revision;

import net.devgrip.commons.utils.PlanarRange;
import net.devgrip.server.codequality.CodeProblem;
import net.devgrip.server.codequality.CoverageStatus;
import net.devgrip.server.model.CodeComment;
import net.devgrip.server.model.CodeCommentReply;
import net.devgrip.server.model.CodeCommentStatusChange;
import net.devgrip.server.model.support.Mark;
import org.apache.wicket.ajax.AjaxRequestTarget;

import javax.annotation.Nullable;
import java.io.Serializable;
import java.util.Collection;
import java.util.Map;

public interface RevisionAnnotationSupport extends Serializable {

    @Nullable
    Mark getMark();

    @Nullable
    String getMarkUrl(Mark mark);

    void onMark(AjaxRequestTarget target, Mark mark);

    void onUnmark(AjaxRequestTarget target);

    @Nullable
    CodeComment getOpenComment();

    Map<CodeComment, PlanarRange> getOldComments(String blobPath);

    Map<CodeComment, PlanarRange> getNewComments(String blobPath);

    Collection<CodeProblem> getOldProblems(String blobPath);

    Collection<CodeProblem> getNewProblems(String blobPath);

    Map<Integer, CoverageStatus> getOldCoverages(String blobPath);

    Map<Integer, CoverageStatus> getNewCoverages(String blobPath);

    void onCommentOpened(AjaxRequestTarget target, CodeComment comment);

    void onCommentClosed(AjaxRequestTarget target);

    void onAddComment(AjaxRequestTarget target, Mark mark);

    void onSaveComment(CodeComment comment);

    void onSaveCommentReply(CodeCommentReply reply);

    void onSaveCommentStatusChange(CodeCommentStatusChange change, @Nullable String note);

}
