package net.devgrip.server.web.page.admin.labelmanagement;

import net.devgrip.server.AppServer;
import net.devgrip.server.annotation.ClassValidating;
import net.devgrip.server.annotation.Editable;
import net.devgrip.server.annotation.OmitName;
import net.devgrip.server.i18n.I18nManager;
import net.devgrip.server.model.LabelSpec;
import net.devgrip.server.validation.Validatable;

import javax.validation.ConstraintValidatorContext;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;

@Editable
@ClassValidating
public class LabelManagementBean implements Serializable, Validatable {

	private static final long serialVersionUID = 1L;

	private List<LabelSpec> labels = new ArrayList<>();

	@Editable
	@OmitName
	public List<LabelSpec> getLabels() {
		return labels;
	}

	public void setLabels(List<LabelSpec> labels) {
		this.labels = labels;
	}

	@Override
	public boolean isValid(ConstraintValidatorContext context) {
		var labelNames = new HashSet<>();
		for (var label: labels) {
			String labelName = label.getName();
			if (!labelNames.add(labelName)) {
				context.disableDefaultConstraintViolation();
				String msg = AppServer.getInstance(I18nManager.class).get("LabelManagementBean.nameDup");
				context.buildConstraintViolationWithTemplate(msg + labelName)
						.addPropertyNode("labels").addConstraintViolation();
				return false;
			}
		}
		return true;
	}
	
}
