package net.devgrip.server.web.component.diff.revision;

import net.devgrip.server.AppServer;
import net.devgrip.server.annotation.Editable;
import net.devgrip.server.annotation.Multiline;
import net.devgrip.server.annotation.OmitName;
import net.devgrip.server.i18n.I18nManager;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

@Editable(name="SuggestionBatchApplyBean.name")
public class SuggestionBatchApplyBean implements Serializable {

	private static final long serialVersionUID = 1L;

	private String commitMessage = "Apply suggested changes from code comments";

	@Editable
	@OmitName
	@Multiline
	@NotEmpty
	public String getCommitMessage() {
		return getI18nManager().getOrDefault("SuggestionBatchApplyBean.defaultCommitMessage", commitMessage);
	}

	public void setCommitMessage(String commitMessage) {
		this.commitMessage = commitMessage;
	}

	private I18nManager getI18nManager() {
		return AppServer.getInstance(I18nManager.class);
	}
	
}
