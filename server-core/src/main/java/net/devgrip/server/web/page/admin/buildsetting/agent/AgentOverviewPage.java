package net.devgrip.server.web.page.admin.buildsetting.agent;

import net.devgrip.server.AppServer;
import net.devgrip.server.entitymanager.AgentAttributeManager;
import net.devgrip.server.entitymanager.AgentManager;
import net.devgrip.server.entitymanager.AgentTokenManager;
import net.devgrip.server.model.AgentAttribute;
import net.devgrip.server.web.component.AgentStatusBadge;
import net.devgrip.server.web.component.link.copytoclipboard.CopyToClipboardLink;
import net.devgrip.server.web.editable.BeanContext;
import net.devgrip.server.web.util.ConfirmClickModifier;
import org.apache.wicket.Session;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.link.Link;
import org.apache.wicket.markup.html.list.ListItem;
import org.apache.wicket.markup.html.list.ListView;
import org.apache.wicket.markup.html.panel.Fragment;
import org.apache.wicket.model.AbstractReadOnlyModel;
import org.apache.wicket.model.LoadableDetachableModel;
import org.apache.wicket.request.mapper.parameter.PageParameters;

import java.util.*;

public class AgentOverviewPage extends AgentDetailPage {

	public AgentOverviewPage(PageParameters params) {
		super(params);
	}

	private AgentManager getAgentManager() {
		return AppServer.getInstance(AgentManager.class);
	}
	
	@Override
	protected void onInitialize() {
		super.onInitialize();
		String restartSended = getString("AgentOverviewPage.restartSended");
		String restartConfirm = getString("AgentOverviewPage.restartConfirm");
		String removed = getString("AgentOverviewPage.removed");
		String removeConfirm = getString("AgentOverviewPage.removeConfirm");
		String resume = getString("AgentOverviewPage.resume");
		String pause = getString("AgentOverviewPage.pause");
		String tokenRegen = getString("AgentOverviewPage.atr");
		String attrSaved = getString("AgentOverviewPage.attrSaved");
		add(new Link<Void>("restart") {

			@Override
			public void onClick() {
				getAgentManager().restart(getAgent());
				setResponsePage(AgentOverviewPage.class, AgentOverviewPage.paramsOf(getAgent()));
				Session.get().success(restartSended);
			}
			
		}.add(new ConfirmClickModifier(restartConfirm)));
		
		add(new Link<Void>("remove") {

			@Override
			public void onClick() {
				getAgentManager().delete(getAgent());
				setResponsePage(AgentListPage.class);
				Session.get().success(removed);
			}

		}.add(new ConfirmClickModifier(removeConfirm)));
		
		add(new Link<Void>("pauseOrResume") {

			@Override
			protected void onInitialize() {
				super.onInitialize();
				add(new Label("label", new AbstractReadOnlyModel<String>() {

					@Override
					public String getObject() {
						return getAgent().isPaused()?resume:pause;
					}
					
				}));
			}

			@Override
			public void onClick() {
				if (getAgent().isPaused())
					getAgentManager().resume(getAgent());
				else
					getAgentManager().pause(getAgent());
				setResponsePage(AgentOverviewPage.class, AgentOverviewPage.paramsOf(getAgent()));
			}
			
		});
		
		add(new AgentIcon("icon", agentModel));
		
		add(new Label("name", getAgent().getName()));
		add(new Label("ipAddress", getAgent().getIpAddress()));
		add(new Label("os", getAgent().getOsName() + getAgent().getOsVersion()));
		add(new Label("osArch", getAgent().getOsArch()));
		add(new AgentStatusBadge("status", agentModel));

		add(new Label("accessToken", new AbstractReadOnlyModel<>() {
			@Override
			public Object getObject() {
				return getAgent().getToken().getValue();
			}
		}));
		add(new CopyToClipboardLink("copyAccessToken", new AbstractReadOnlyModel<>() {
			@Override
			public String getObject() {
				return getAgent().getToken().getValue();
			}
		}));
		add(new Link<Void>("regenerateAccessToken") {

			@Override
			public void onClick() {
				var token = getAgent().getToken();
				token.setValue(UUID.randomUUID().toString());
				AppServer.getInstance(AgentTokenManager.class).createOrUpdate(token);
				AppServer.getInstance(AgentManager.class).disconnect(getAgent().getId());
				Session.get().success(tokenRegen);
				setResponsePage(AgentOverviewPage.class, paramsOf(getAgent()));
			}
		});
		
		if (getAgent().isOnline()) {
			Fragment fragment = new Fragment("attributes", "onlineAttributesFrag", this);
			AgentEditBean bean = new AgentEditBean();
			bean.getAttributes().addAll(getAgent().getAttributes());
			bean.getAttributes().sort(Comparator.comparing(AgentAttribute::getName));
			
			Form<?> form = new Form<Void>("form") {

				@Override
				protected void onSubmit() {
					super.onSubmit();
					Map<String, String> attributeMap = new HashMap<>();
					for (AgentAttribute attribute: bean.getAttributes())
						attributeMap.put(attribute.getName(), attribute.getValue());
					AppServer.getInstance(AgentAttributeManager.class).syncAttributes(getAgent(), attributeMap);
					getAgentManager().attributesUpdated(getAgent());
					Session.get().success(attrSaved);
				}
				
			};
			form.add(BeanContext.edit("attributes", bean));
			fragment.add(form);
			add(fragment);
		} else if (!getAgent().getAttributes().isEmpty()) {
			Fragment fragment = new Fragment("attributes", "offlineHasAttributesFrag", this);
			fragment.add(new ListView<>("attributes", new LoadableDetachableModel<List<AgentAttribute>>() {

				@Override
				protected List<AgentAttribute> load() {
					List<AgentAttribute> attributes = new ArrayList<>(getAgent().getAttributes());
					attributes.sort(Comparator.comparing(AgentAttribute::getName));
					return attributes;
				}

			}) {

				@Override
				protected void populateItem(ListItem<AgentAttribute> item) {
					item.add(new Label("name", item.getModelObject().getName()));
					item.add(new Label("value", item.getModelObject().getValue()));
				}

				@Override
				protected void onConfigure() {
					super.onConfigure();
					setVisible(!getModelObject().isEmpty());
				}

			});
			add(fragment);
		} else {
			add(new Fragment("attributes", "offlineNoAttributesFrag", this));
		}
	}

}
