:root {
	--sidebar-width: 230px;
	--sidebar-mini-width: 51px;
	--topbar-height: 64px;	
	--transition-duration: 0.3s;
	--brand-size: 50px;
	--top-title-height: 30px;
	--breadcrumb-height: 40px;
	--topBar-background:var(--primary);
	--topBar-color:var(--primary-contrast);
}

.TopbarOnlyLayoutPage>body {
	position: static !important;
}
.btn-label {
	padding: 0.2rem 0.6rem;
	font-size: 0.875rem;
	font-weight: 500;
	border-radius: 0.5rem;
	border: none;
	text-transform: capitalize;
}
.topbar {
	position: fixed;
	display: flex;
	align-items: center;
	top: 0;
	left: 0;
	right: 0;
	height: var(--topbar-height);
	background: var(--topBar-background);
	padding: 0 0.8rem 0 1.2rem;
	z-index: 1030;
	box-shadow: 0px 0px 4px 0px rgba(0,0,0,0.1);
	transition: all var(--transition-duration) ease;
}
/**
顶部导航 底部彩虹条
 */
.topbar::after {
	content: ''; /* 伪元素 */
	position: absolute;
	bottom: 0;
	left: 0;
	width: 100%;
	height: 2px; /* 设置底部 border 的厚度 */
	background: linear-gradient(to right, orange,yellow, green,indigo, blue, red, violet); /* 彩虹渐变色 */
}

.dark-mode .topbar {
	background: var(--dark-mode-dark);
	box-shadow: 0 0 12px rgb(0 0 0 / 50%);	
	color: white;
}
.topbar-left {
	display: flex;
	align-items: center;
	flex-grow: 1;
	min-width: 1px;
}
.topbar-brand {
	display: none;
	margin: 0 1rem 0 -0.4rem;
	color: var(--gray-dark) !important;
}
.topbar-brand img {
	width: var(--brand-size);
	height: var(--brand-size);
}
.dark-mode .topbar-brand {
	color: white !important;
}

.topnav-brand {
	display: flex;
	align-items: center;
	justify-content: space-between;
	width: var(--sidebar-width);
	font-weight: 500;
	font-size: 1.4rem;
	
}
.topnav-brand>a {
	display: flex;
	align-items: center;
	color:var(--topBar-color);
}

.dark-mode .topnav-brand>a {
	color: var(--dark-link-color) !important; /* 确保优先级更高 */
}

.topnav-brand>a:hover {
	font-size: 1.5rem;
}


.topbar-brand svg {
	width: var(--brand-size);
	height: var(--brand-size);
}
.topnav {
	border-bottom: 0;
	font-weight: 500;
}
.topnav-brand svg, .topnav-brand img {
	width: var(--brand-size);
	height: var(--brand-size);
	margin-right: 1rem;
}

.topbar-left > .nav .nav-link {
	color:var(--topBar-color);
	font-family: Roboto, system-ui, sans-serif;
	letter-spacing: 0.02em;
	font-weight: 400;
}
.topbar-left > .nav.nav-tabs.nav-tabs-line .nav-link:hover:not(.disabled),
.topbar-left > .nav.nav-tabs.nav-tabs-line .active .nav-link,
.topbar-left > .nav.nav-tabs.nav-tabs-line .nav-link.active,
.topbar-left > .nav.nav-tabs.nav-tabs-line .show > .nav-link {
	color:var(--topBar-color);
	font-family: Roboto, system-ui, sans-serif;
	background-color: transparent;
	border: 0;
	border-bottom: 3px solid var(--topBar-color);
}

.dark-mode .topbar-left > .nav.nav-tabs.nav-tabs-line .nav-link:hover:not(.disabled),
.dark-mode .topbar-left > .nav.nav-tabs.nav-tabs-line .active .nav-link,
.dark-mode .topbar-left > .nav.nav-tabs.nav-tabs-line .nav-link.active,
.dark-mode .topbar-left > .nav.nav-tabs.nav-tabs-line .show > .nav-link {
	color:var(--primary);
	border-bottom: 3px solid var(--primary);
}


.topbar-right {
	display: flex;
	align-items: center;
}
.topbar input.search {
	width: 100%;
}
.topbar-right-icon{
	width: 20px;
	height: 20px;
	fill: white;
}

.dark-mode .topbar-right .topbar-right-icon {
	fill: var(--dark-mode-gray);
}

.topbar-right a.topbar-link {
	padding: 0.5rem;
	border-radius: 0.42rem;
}
.topbar-right div {
	margin-bottom: 0.1rem;
	border-radius: 0.42rem;
}
.topbar-right div > a > span {
	color: var(--topBar-color);
}
.white-span {
	color: var(--topBar-color);
}

.topbar-right .new-version-status {
	vertical-align: middle;
}
.topbar-right .new-version-status>img {
	margin-top: -3px;
}
.topbar-right a.topbar-link:hover, 
.topbar-right .active>a.topbar-link, 
.topbar-right .show>a.topbar-link {
    background: var(--primary-focus);
}
.dark-mode .topbar-right a.topbar-link:hover, 
.dark-mode .topbar-right .active>a.topbar-link, 
.dark-mode .topbar-right .show>a.topbar-link {
    background: var(--dark-mode-light-dark);
}

.dark-mode .topbar-left .nav .nav-link {
	color: var(--dark-link-color);
}

.cp {
	border: 1px solid currentcolor;
	border-radius: 4px;
	padding-left: 4px;
	padding-right: 4px;
	color: white;
}

.dark-mode .cp {
	color: var(--dark-mode-gray);
}

.TopbarOnlyLayoutPage>body>.main {
	transition: all var(--transition-duration) ease;
	position: absolute;
	top: var(--topbar-height);
	left: 0;
	right: 0;
	bottom: 0;
}

.floating .alerts {
	max-width: 640px;
}

.topNav-toggle {
	min-width: 36px;
	min-height: 36px;
	color: var(--gray);
	display: flex;
	align-items: center;
}
.topNav-toggle svg {
	width: 24px;
	height: 24px;
}

.icon.teal-color-icon {
	fill: var(--teal-9) ;
	width: 16px;
	height: 16px;
}
.icon.violet-color-icon {
	fill: var(--violet-9) ;
	width: 16px;
	height: 16px;
}
.icon.pastelpink-color-icon {
	fill: var(--pastelpink-9) ;
	width: 16px;
	height: 16px;
}
.icon.indigo-color-icon {
	fill: var(--indigo) ;
	width: 16px;
	height: 16px;
}
.icon.crimson-color-icon {
	fill: var(--crimson-9) ;
	width: 16px;
	height: 16px;
}
.icon.olivegreen-color-icon {
	fill: var(--olivegreen-9) ;
	width: 16px;
	height: 16px;
}
.icon.caramelbrown-color-icon {
	fill: var(--caramelbrown-9) ;
	width: 16px;
	height: 16px;
}

