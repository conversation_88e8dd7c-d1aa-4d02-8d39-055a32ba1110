package net.devgrip.server.web.page.admin.authenticator;

import net.devgrip.server.annotation.Editable;
import net.devgrip.server.model.support.administration.authenticator.Authenticator;

import java.io.Serializable;

@Editable
public class AuthenticatorBean implements Serializable {

	private static final long serialVersionUID = 1L;

	private Authenticator authenticator;

	@Editable(placeholder="AuthenticatorBean.noAuth")
	public Authenticator getAuthenticator() {
		return authenticator;
	}

	public void setAuthenticator(Authenticator authenticator) {
		this.authenticator = authenticator;
	}

}
