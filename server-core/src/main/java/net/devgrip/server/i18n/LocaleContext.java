package net.devgrip.server.i18n;

import net.devgrip.commons.utils.StringUtils;
import net.devgrip.server.AppServer;

import javax.annotation.Nonnull;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;

/**
 * 语言上下文,目前系统仅支持英语(默认)，简体中文，繁体中文(台湾)，繁体中文(香港)。
 * 另外请参考{@link DefaultI18nManager}
 */

public final class LocaleContext {

	public static final List<Locale> SUPPORTED_LOCALES = Arrays.asList(Locale.ENGLISH, Locale.SIMPLIFIED_CHINESE, Locale.JAPAN);
	public static final Locale DEFAULT_LOCALE = Locale.ENGLISH;
	public static final String LOCALE_SPLITTER = "_";


	/**
	 * 获取用户设置的语言，之后用该语言来进行国际化展示，默认系统以英语显示。
	 * @return 系统的locale
	 */
	@Nonnull
	public static Locale getSystemCurrentLocale() {
		I18nManager instance = AppServer.getInstance(I18nManager.class);
		if (instance != null) {
			Locale currentLocale = instance.getCurrentLocale();
			if (currentLocale != null) {
				return currentLocale;
			}
		}
		return DEFAULT_LOCALE;
	}

	public static Locale parseStrLocale(String locale) {
		if (StringUtils.isBlank(locale)) {
			return DEFAULT_LOCALE;
		}
		String[] split = locale.split(LOCALE_SPLITTER);
		String lang = split[0];
		String country = "";
		if (split.length > 1) {
			country = split[1];
		}
		return new Locale(lang, country);
	}
	
}
