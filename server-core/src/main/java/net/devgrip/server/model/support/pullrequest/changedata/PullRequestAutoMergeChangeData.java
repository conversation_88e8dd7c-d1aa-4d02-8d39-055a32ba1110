package net.devgrip.server.model.support.pullrequest.changedata;

public class PullRequestAutoMergeChangeData extends PullRequestChangeData {

	private static final long serialVersionUID = 1L;

	private final boolean enabled;
	
	public PullRequestAutoMergeChangeData(boolean enabled) {
		this.enabled = enabled;
	}
	
	@Override
	public String getActivity() {
		return enabled? "enabled auto merge": "disabled auto merge";
	}

	/**
	 * 获取动态的I18n的key，用于国际化显示
	 *
	 * @return
	 */
	@Override
	public String getActivityI18nKey() {
		return enabled ? "PullRequestChangeData.activities.enabledAutoMerge" : "PullRequestChangeData.activities.disabledAutoMerge";
	}

}
