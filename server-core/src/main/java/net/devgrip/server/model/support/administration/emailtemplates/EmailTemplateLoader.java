package net.devgrip.server.model.support.administration.emailtemplates;

import com.google.common.io.Resources;
import net.devgrip.commons.utils.StringUtils;
import net.devgrip.server.i18n.LocaleContext;

import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.Locale;
import java.util.Objects;

/**
 * email模版加载器
 */
public class EmailTemplateLoader {

	private static class Holder {
		private static final EmailTemplateLoader INSTANCE = new EmailTemplateLoader();
	}
	public static EmailTemplateLoader getInstance() {
		return Holder.INSTANCE;
	}
	
	private String deufaltSimpleNotification;
	private String defualtNotification;
	private String defaultIssueNotificationUnsubscribed;
	private String defaultPullRequestNotificationUnsubscribed;
	private String defaultServiceDeskIssueOpened;
	private String defaultServiceDeskIssueOpenFailed;
	private String defaultUserInvitation;
	private String defaultEmailVerification;
	private String defaultPasswordReset;
	private String defaultStopwatchOverdue;
	private String defaultAlert;
	
	private EmailTemplateLoader() {
		initialize();
	}

	public synchronized void initialize() {
		deufaltSimpleNotification = initializeSimpleNotification();
		defualtNotification = initializeDefaultNotification();
		defaultIssueNotificationUnsubscribed = initializeIssueNotificationUnsubscribed();
		defaultPullRequestNotificationUnsubscribed = initializePullRequestNotificationUnsubscribed();
		defaultServiceDeskIssueOpened = initializeServiceDeskIssueOpened();
		defaultServiceDeskIssueOpenFailed = initializeServiceDeskIssueOpeneFailed();
		defaultUserInvitation = initializeUserInvitation();
		defaultEmailVerification = initializeEmailVerification();
		defaultPasswordReset = initializePasswordReset();
		defaultStopwatchOverdue = initializeStopwatchOverdue();
		defaultAlert = initializeAlert();
	}

	private String initializeSimpleNotification(){

		URL url = Resources.getResource(EmailTemplateLoader.class, getLangDirPrefix() + "default-simple-notification.tpl");
		try {
			return Resources.toString(url, StandardCharsets.UTF_8);
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
	}
	private String initializeDefaultNotification(){

		URL url = Resources.getResource(EmailTemplateLoader.class, getLangDirPrefix() + "default-notification.tpl");
		try {
			return Resources.toString(url, StandardCharsets.UTF_8);
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
	}
	private String initializeServiceDeskIssueOpened(){

		URL url = Resources.getResource(EmailTemplateLoader.class, getLangDirPrefix() + "default-service-desk-issue-opened.tpl");
		try {
			return Resources.toString(url, StandardCharsets.UTF_8);
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
	}
	private String initializeServiceDeskIssueOpeneFailed(){

		URL url = Resources.getResource(EmailTemplateLoader.class, getLangDirPrefix() + "default-service-desk-issue-open-failed.tpl");
		try {
			return Resources.toString(url, StandardCharsets.UTF_8);
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
	}
	private String initializeIssueNotificationUnsubscribed(){

		URL url = Resources.getResource(EmailTemplateLoader.class, getLangDirPrefix() + "default-issue-notification-unsubscribed.tpl");
		try {
			return Resources.toString(url, StandardCharsets.UTF_8);
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
	}
	private String initializePullRequestNotificationUnsubscribed(){

		URL url = Resources.getResource(EmailTemplateLoader.class, getLangDirPrefix() + "default-pull-request-notification-unsubscribed.tpl");
		try {
			return Resources.toString(url, StandardCharsets.UTF_8);
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
	}
	private String initializeUserInvitation(){

		URL url = Resources.getResource(EmailTemplateLoader.class, getLangDirPrefix() + "default-user-invitation.tpl");
		try {
			return Resources.toString(url, StandardCharsets.UTF_8);
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
	}
	private String initializeEmailVerification(){

		URL url = Resources.getResource(EmailTemplateLoader.class, getLangDirPrefix() + "default-email-verification.tpl");
		try {
			return Resources.toString(url, StandardCharsets.UTF_8);
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
	}
	private String initializePasswordReset(){

		URL url = Resources.getResource(EmailTemplateLoader.class, getLangDirPrefix() + "default-password-reset.tpl");
		try {
			return Resources.toString(url, StandardCharsets.UTF_8);
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
	}
	private String initializeStopwatchOverdue(){

		URL url = Resources.getResource(EmailTemplateLoader.class, getLangDirPrefix() + "default-stopwatch-overdue.tpl");
		try {
			return Resources.toString(url, StandardCharsets.UTF_8);
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
	}
	private String initializeAlert(){

		URL url = Resources.getResource(EmailTemplateLoader.class, getLangDirPrefix() + "default-alert.tpl");
		try {
			return Resources.toString(url, StandardCharsets.UTF_8);
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
	}

	private static String getLangDirPrefix() {
		Locale currentLocale = LocaleContext.getSystemCurrentLocale();
		if(!Objects.equals(currentLocale, LocaleContext.DEFAULT_LOCALE)) {
			String language = currentLocale.getLanguage();
			String country = currentLocale.getCountry();
			String dir = language;
			if (StringUtils.isNotBlank(country)) {
				dir = language + country;
			}
			return dir + File.separator;
		}
		return "";
	}

	public String getDeufaltSimpleNotification() {
		return deufaltSimpleNotification;
	}

	public String getDefualtNotification() {
		return defualtNotification;
	}

	public String getDefaultIssueNotificationUnsubscribed() {
		return defaultIssueNotificationUnsubscribed;
	}

	public String getDefaultPullRequestNotificationUnsubscribed() {
		return defaultPullRequestNotificationUnsubscribed;
	}

	public String getDefaultServiceDeskIssueOpened() {
		return defaultServiceDeskIssueOpened;
	}

	public String getDefaultServiceDeskIssueOpenFailed() {
		return defaultServiceDeskIssueOpenFailed;
	}

	public String getDefaultUserInvitation() {
		return defaultUserInvitation;
	}

	public String getDefaultEmailVerification() {
		return defaultEmailVerification;
	}

	public String getDefaultPasswordReset() {
		return defaultPasswordReset;
	}

	public String getDefaultStopwatchOverdue() {
		return defaultStopwatchOverdue;
	}

	public String getDefaultAlert() {
		return defaultAlert;
	}
}
