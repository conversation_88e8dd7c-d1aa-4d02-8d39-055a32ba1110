package net.devgrip.server.model.support.administration;

import net.devgrip.server.AppServer;
import net.devgrip.server.annotation.Editable;
import net.devgrip.server.annotation.GroupChoice;
import net.devgrip.server.annotation.Patterns;
import net.devgrip.server.annotation.ShowCondition;
import net.devgrip.server.entitymanager.GroupManager;
import net.devgrip.server.i18n.I18nManager;
import net.devgrip.server.model.Group;
import net.devgrip.server.util.EditContext;
import net.devgrip.server.util.usage.Usage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Nullable;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Editable
public class SecuritySetting implements Serializable {

	private static final long serialVersionUID = 1L;
	
	private static final Logger logger = LoggerFactory.getLogger(SecuritySetting.class);

	private boolean enableAnonymousAccess = false;
	
	private boolean enableSelfRegister = true;
	
	private String allowedSelfRegisterEmailDomain;
	
	private boolean enableSelfDeregister;
	
	private String defaultGroupName;
	
	private boolean enforce2FA;

	private PasswordRule passwordRule;
	
	private List<String> corsAllowedOrigins = new ArrayList<>();
	
	@Editable(order=100, name= "security.enableAnonymousAccess", description = "security.enableAnonymousAccess.description")
	public boolean isEnableAnonymousAccess() {
		return enableAnonymousAccess;
	}

	public void setEnableAnonymousAccess(boolean enableAnonymousAccess) {
		this.enableAnonymousAccess = enableAnonymousAccess;
	}

	@Editable(order=200, name="security.enableSelfRegister.name", description="security.enableSelfRegister.description")
	public boolean isEnableSelfRegister() {
		return enableSelfRegister;
	}

	public void setEnableSelfRegister(boolean enableSelfRegister) {
		this.enableSelfRegister = enableSelfRegister;
	}

	@Editable(order=225, name="security.allowedSelfRegisterEmailDomain.name", placeholder = "security.allowedSelfRegisterEmailDomain.placeholder", description = "security.allowedSelfRegisterEmailDomain.description")
	@Patterns
	@ShowCondition("isEnableSelfRegisterEnabled")
	public String getAllowedSelfRegisterEmailDomain() {
		return allowedSelfRegisterEmailDomain;
	}

	public void setAllowedSelfRegisterEmailDomain(String allowedSelfRegisterEmailDomain) {
		this.allowedSelfRegisterEmailDomain = allowedSelfRegisterEmailDomain;
	}
	
	@SuppressWarnings("unused")
	private static boolean isEnableSelfRegisterEnabled() {
		return (boolean) EditContext.get().getInputValue("enableSelfRegister");
	}

	@Editable(order=300, name= "security.defaultGroup.name", description= "security.defaultGroup.description")
	@GroupChoice
	public String getDefaultGroupName() {
		return defaultGroupName;
	}

	public void setDefaultGroupName(String defaultGroupName) {
		this.defaultGroupName = defaultGroupName;
	}

	@Editable(order=350, name="security.enableSelfDeregister.name", description = "security.enableSelfDeregister.description")
	public boolean isEnableSelfDeregister() {
		return enableSelfDeregister;
	}

	public void setEnableSelfDeregister(boolean enableSelfDeregister) {
		this.enableSelfDeregister = enableSelfDeregister;
	}

	@Editable(order=400, name="security.enforce2FA.name", description="security.enforce2FA.description")
	public boolean isEnforce2FA() {
		return enforce2FA;
	}

	public void setEnforce2FA(boolean enforce2FA) {
		this.enforce2FA = enforce2FA;
	}

	@Editable(order=250, name="security.enforcePwdRule.name", description="security.enforcePwdRule.desc")
	public PasswordRule getPasswordRule() {
		return passwordRule;
	}

	public void setPasswordRule(PasswordRule passwordRule) {
		this.passwordRule = passwordRule;
	}

	@Editable(order=500, name="security.corsAllowedOrigins.name", placeholder = "security.corsAllowedOrigins.placeholder", description = "security.corsAllowedOrigins.description")
	public List<String> getCorsAllowedOrigins() {
		return corsAllowedOrigins;
	}

	public void setCorsAllowedOrigins(List<String> corsAllowedOrigins) {
		this.corsAllowedOrigins = corsAllowedOrigins;
	}

	@Nullable
	public Group getDefaultGroup() {
		if (defaultGroupName != null) {
       		Group group = AppServer.getInstance(GroupManager.class).find(defaultGroupName);
       		if (group == null)
                logger.error("Unable to find default group: {}", defaultGroupName);
       		else
       			return group;
		}
		return null;
	}
	
	public void onRenameGroup(String oldName, String newName) {
		if (oldName.equals(defaultGroupName))
			defaultGroupName = newName;
	}
	
	public Usage onDeleteGroup(String groupName) {
		Usage usage = new Usage();
		I18nManager i18nManager = AppServer.getInstance(I18nManager.class);
		if (groupName.equals(defaultGroupName)) {
			String desc = i18nManager.getOrDefault("security.usage.desc", "default group for sign-in users");
			usage.add(desc);
		}
		return usage.prefix(i18nManager.getOrDefault("security.usage.prefix", "security settings"));
	}
	
}
