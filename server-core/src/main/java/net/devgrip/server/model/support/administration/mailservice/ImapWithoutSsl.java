package net.devgrip.server.model.support.administration.mailservice;

import net.devgrip.server.annotation.Editable;

import java.util.Properties;

@Editable(order=300, name="SmtpImapSSL.noSSL.name")
public class ImapWithoutSsl implements ImapSslSetting {

	private static final long serialVersionUID = 1L;
	
	private int port = 143;

	@Editable(name="SmtpImapSSL.port", description = "SmtpImapMailService.ssl.desc")
	public int getPort() {
		return port;
	}

	public void setPort(int port) {
		this.port = port;
	}

	@Override
	public void configure(Properties properties) {
		properties.setProperty("mail.imap.port", String.valueOf(port));
		properties.setProperty("mail.smtp.localhost", "localhost.localdomain");
	}
	
}
