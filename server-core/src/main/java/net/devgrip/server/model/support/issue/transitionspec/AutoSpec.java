package net.devgrip.server.model.support.issue.transitionspec;

import com.google.common.collect.Lists;
import net.devgrip.server.annotation.ChoiceProvider;
import net.devgrip.server.annotation.Editable;
import net.devgrip.server.web.component.issue.workflowreconcile.UndefinedStateResolution;

import javax.validation.constraints.NotEmpty;
import java.util.Collection;
import java.util.List;
import java.util.Map;

@Editable
public abstract class AutoSpec extends TransitionSpec {
	
	private String toState;

	@Editable(order=50,name = "ToState.name",placeholder = "Please_Choose")
	@NotEmpty
	@ChoiceProvider("getStateChoices")
	public String getToState() {
		return toState;
	}

	public void setToState(String toState) {
		this.toState = toState;
	}

	@Override
	public Collection<String> getUndefinedStates() {
		Collection<String> undefinedStates = super.getUndefinedStates();
		if (getIssueSetting().getStateSpec(toState) == null)
			undefinedStates.add(toState);
		return undefinedStates;
	}

	@Override
	public boolean fixUndefinedStates(Map<String, UndefinedStateResolution> resolutions) {
		if (!super.fixUndefinedStates(resolutions))
			return false;
		for (Map.Entry<String, UndefinedStateResolution> entry: resolutions.entrySet()) {
			if (entry.getValue().getFixType() == UndefinedStateResolution.FixType.CHANGE_TO_ANOTHER_STATE) {
				if (toState.equals(entry.getKey()))
					toState = entry.getValue().getNewState();
			} else if (toState.equals(entry.getKey())) {
				return false;
			}
		}
		return true;
	}

	@Override
	public List<String> getToStates() {
		return Lists.newArrayList(getToState());
	}

}
