@echo off
rem quotes are required for correct handling of path with spaces

set "wrapper_home=%~dp0/.."
set "devgrip_agent_home=%~dp0/../.."

for %%I in ("%wrapper_home%") do set "wrapper_home=%%~fI"
for %%I in ("%devgrip_agent_home%") do set "devgrip_agent_home=%%~fI"


rem default java exe for running the wrapper
rem note this is not the java exe for running the application. the exe for running the application is defined in the wrapper configuration file
rem check JAVA_HOME and java in Path
rem Detect java path
if defined JAVA_HOME (
    set "java_exe=%JAVA_HOME%\bin\java"
    set "javaw_exe=%JAVA_HOME%\bin\javaw"
) else (
    for %%i in (java.exe) do set "java_exe=%%~$PATH:i"
    for %%i in (javaw.exe) do set "javaw_exe=%%~$PATH:i"
)

if not defined java_exe (
    echo ERROR: JAVA_HOME is not set and 'java' not found in PATH.
    exit /b 1
)

rem Extract version string (third token of first line)
for /f "tokens=3" %%A in ('"%java_exe%" -version 2^>^&1') do (
    set "JAVA_VERSION_RAW=%%A"
    goto :after_version
)
:after_version

setlocal EnableDelayedExpansion
rem Strip surrounding quotes
set "JAVA_VERSION=!JAVA_VERSION_RAW:"=!"

rem Parse major version
for /f "tokens=1,2 delims=." %%A in ("!JAVA_VERSION!") do (
    if "%%A"=="1" (
        rem Java 8 or lower: version starts with 1.X
        set "JAVA_MAJOR=%%B"
    ) else (
        rem Java 9 or higher
        set "JAVA_MAJOR=%%A"
    )
)

if not defined JAVA_MAJOR (
    echo ERROR: Failed to detect Java version.
    exit /b 1
)

echo Detected Java major version: !JAVA_MAJOR!

if !JAVA_MAJOR! LSS 11 (
    echo ERROR: Java version !JAVA_MAJOR! is too old. Java 11 or higher is required.
    exit /b 1
)

echo Java version OK: !JAVA_MAJOR!
endlocal

rem location of the wrapper jar file. necessary lib files will be loaded by this jar. they must be at <wrapper_home>/lib/...
set wrapper_jar="%wrapper_home%\wrapper.jar"
set wrapper_app_jar="%wrapper_home%\wrapperApp.jar"
set wrapper_app9_jar="%wrapper_home%\wrapperApp9.jar"

set wrapper_java_options=-Xmx30m -Dwrapper_home="%wrapper_home%" -Djna_tmpdir="%wrapper_home%/tmp" -Djava.net.preferIPv4Stack=true

rem wrapper bat file for running the wrapper
set wrapper_bat="%wrapper_home%\bat\wrapper.bat"
set wrapperw_bat="%wrapper_home%\bat\wrapperW.bat"

rem configuration file used by all bat files
set conf_file="%devgrip_agent_home%\conf\wrapper.conf"
