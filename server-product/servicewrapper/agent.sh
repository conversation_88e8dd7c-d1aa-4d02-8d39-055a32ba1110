#!/usr/bin/env bash
# -----------------------------------------------------------------------------
# Control script for devgrip agent: console | start | stop | status
# -----------------------------------------------------------------------------

set -euo pipefail


echo '++++++++++ SET ENV ++++++++++'

#remember current dir
current=$(pwd)
# resolve links - $0 may be a softlink
PRGDIR=$(dirname $0)

cd "$PRGDIR"

# path to yajsw bin folder
PRGDIR=$(pwd)

cd ".."

# path to wrapper home
devgrip_agent_home=$(pwd)
export devgrip_agent_home

cd "boot"

# path to wrapper home
wrapper_home=$(pwd)
export wrapper_home

# return to original folder
cd "$current"

wrapper_jar="$wrapper_home"/wrapper.jar
export wrapper_jar

wrapper_app_jar="$wrapper_home"/wrapperApp.jar
export wrapper_app_jar

wrapper_app9_jar="$wrapper_home"/wrapperApp9.jar
export wrapper_app9_jar

wrapper_java_sys_options=-Djna_tmpdir="$wrapper_home"/tmp
export wrapper_java_sys_options

wrapper_java_options=-Xmx30m
export wrapper_java_options

# Detect java binary
if [ "${JAVA_HOME+x}" ] && [ -x "$JAVA_HOME/bin/java" ]; then
  java_exe="$JAVA_HOME/bin/java"
elif command -v java >/dev/null 2>&1; then
  java_exe=$(command -v java)
else
  echo "ERROR: JAVA_HOME is not set and 'java' is not found in PATH."
  echo "Please set the JAVA_HOME environment variable to your Java installation directory."
  exit 1
fi
export java_exe

# show java version
"$java_exe" -version

JAVA_VERSION_OUTPUT=$("$java_exe" -version 2>&1)
JAVA_MAJOR_VERSION=$(echo "$JAVA_VERSION_OUTPUT" | sed -E -n 's/.* version "([0-9]+)\..*/\1/p')

# java 1.8 => 8
if [ "$JAVA_MAJOR_VERSION" = "1" ]; then
  JAVA_MAJOR_VERSION=$(echo "$JAVA_VERSION_OUTPUT" | sed -E -n 's/.* version "1\.([0-9]+)\..*/\1/p')
fi

if [ "$JAVA_MAJOR_VERSION" -lt 11 ]; then
  echo "ERROR: JAVA version must be 11 or higher"
  exit 1
fi

conf_file="$devgrip_agent_home"/conf/wrapper.conf
export conf_file

echo "wrapper home : $wrapper_home"
echo "configuration: $conf_file"
echo '---------- SET ENV FINISHED ----------'


# Check required jar files
if [ ! -f "$wrapper_jar" ]; then
  echo "Cannot find wrapper_jar: $wrapper_jar"
  exit 1
fi

if [ ! -f "$wrapper_app_jar" ]; then
  echo "Cannot find wrapper_app_jar: $wrapper_app_jar"
  exit 1
fi

if [ ! -f "$wrapper_app9_jar" ]; then
  echo "Cannot find wrapper_app9_jar: $wrapper_app9_jar"
  exit 1
fi

if [ -z "$wrapper_java_options" ]; then
  echo "wrapper_java_options is not set"
  exit 1
fi

# Check conf_file
if [ -z "${conf_file:-}" ]; then
  echo "conf_file is not set. Please define it in setenv.sh"
  exit 1
fi

APP_NAME=agent

show_usage() {
  echo "Usage: $0 {start|stop|status|console}"
  echo
  echo "Available commands:"
  echo "  start     Start the $APP_NAME in background"
  echo "  stop      Stop the $APP_NAME gracefully"
  echo "  status    Show current $APP_NAME status"
  echo "  console   Run the $APP_NAME in foreground console"
  echo
  echo "Examples:"
  echo "  $0 start"
  echo "  $0 stop"
  echo "  $0 status"
  echo "  $0 console"
}


if [ $# -ne 1 ]; then
  show_usage
  exit 1
fi

check_is_running() {
  local status_output is_running

  status_output=$(
    "$java_exe" $wrapper_java_options \
      -Djava.net.preferIPv4Stack=true \
      --add-opens=java.base/java.lang=ALL-UNNAMED \
      --add-opens=java.base/java.lang.invoke=ALL-UNNAMED \
      --add-opens=java.base/java.io=ALL-UNNAMED \
      $wrapper_java_sys_options -jar "$wrapper_jar" -q "$conf_file" 2>/dev/null
  )

  is_running=$(echo "$status_output" | awk -F': ' '/^Running/ {print $2}' | xargs)
  if [ "$is_running" = "true" ]; then
    return 0  # running
  else
    return 1  # not running
  fi
}

ACTION=$1

case "$ACTION" in
  console)
    if check_is_running; then
      echo "$APP_NAME is already running."
      exit 0
    fi
    echo "Running $APP_NAME in console (foreground)..."
    exec "$java_exe" "$wrapper_java_options" -Djava.net.preferIPv4Stack=true --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED "$wrapper_java_sys_options" -jar "$wrapper_jar" -c "$conf_file"
    ;;
  start)
    if check_is_running; then
      echo "$APP_NAME is already running."
      exit 0
    fi
    echo "Starting $APP_NAME (in background)..."
    exec "$java_exe" "$wrapper_java_options" -Djava.net.preferIPv4Stack=true --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED "$wrapper_java_sys_options" -jar "$wrapper_jar" -tx "$conf_file"
    ;;

  stop)
    echo "Stopping $APP_NAME..."
    exec "$java_exe" "$wrapper_java_options" -Djava.net.preferIPv4Stack=true --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED "$wrapper_java_sys_options" -jar "$wrapper_jar" -px "$conf_file"
    ;;

  status)
    echo "Checking $APP_NAME status..."
    "$java_exe" "$wrapper_java_options" -Djava.net.preferIPv4Stack=true --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED "$wrapper_java_sys_options" -jar "$wrapper_jar" -q "$conf_file"
    ;;
  *)
    echo "Invalid argument: $ACTION"
    echo
    show_usage
    exit 1
    ;;
esac
